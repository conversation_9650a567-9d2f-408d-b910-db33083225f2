use std::collections::HashMap;
use std::io::{self, Write};
use std::fs;

use crate::command_planner::{CommandPlanner, CommandContext};
use crate::terminal_bridge::{TerminalBridge, ExecutionConfig};
use crate::command_memory::CommandMemory;
use crate::security_layer::SecurityLayer;
use crate::logger::LOGGER;
use crate::config::Config;
use crate::language::Language;
use crate::agent;

pub struct InteractiveSession {
    planner: CommandPlanner,
    bridge: TerminalBridge,
    memory: CommandMemory,
    security: SecurityLayer,
    config: Config,
    current_language: Language,
    session_vars: HashMap<String, String>,
    command_aliases: HashMap<String, String>,
    auto_complete_enabled: bool,
    session_id: String,
    // New fields for enhanced code generation
    conversation_context: Vec<ConversationEntry>,
    current_code: Option<GeneratedCode>,
    code_generation_mode: bool,
}

#[derive(Debug, Clone)]
pub struct ConversationEntry {
    pub user_input: String,
    pub response: String,
    pub timestamp: u64,
    pub code_generated: Option<String>,
}

#[derive(Debug, Clone)]
pub struct GeneratedCode {
    pub content: String,
    pub language: Language,
    pub filename: String,
    pub task_description: String,
    pub last_modified: u64,
}

#[derive(Debug, Clone)]
pub struct SessionConfig {
    pub enable_auto_complete: bool,
    pub enable_command_chaining: bool,
    pub enable_history_search: bool,
    pub max_chain_length: usize,
    pub prompt_style: PromptStyle,
}

#[derive(Debug, Clone)]
pub enum PromptStyle {
    Simple,
    Detailed,
    Minimal,
    Custom(String),
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            enable_auto_complete: true,
            enable_command_chaining: true,
            enable_history_search: true,
            max_chain_length: 5,
            prompt_style: PromptStyle::Detailed,
        }
    }
}

impl InteractiveSession {
    pub fn new(config: Config) -> Result<Self, Box<dyn std::error::Error>> {
        let current_language = Language::from_str(&config.default_language)
            .unwrap_or(Language::Python);
        
        let context = CommandContext {
            current_directory: std::env::current_dir()?.to_string_lossy().to_string(),
            available_tools: vec!["python3".to_string(), "git".to_string(), "ls".to_string()],
            recent_commands: Vec::new(),
            project_type: Some("rust".to_string()),
            language_context: Some(current_language.to_string()),
        };
        
        let planner = CommandPlanner::new(context);
        
        let execution_config = ExecutionConfig {
            timeout_seconds: config.default_timeout,
            capture_output: true,
            working_directory: None,
            environment_vars: HashMap::new(),
            max_output_lines: Some(100),
            stream_output: false,
        };
        
        let bridge = TerminalBridge::new(execution_config);
        
        let config_dir = Config::get_config_path()?.parent().unwrap().to_path_buf();
        let data_dir = config_dir.join("data");
        let memory = CommandMemory::new(data_dir.clone())?;

        let security = SecurityLayer::new(config_dir)?;

        let session_id = format!("session_{}",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        );

        Ok(Self {
            planner,
            bridge,
            memory,
            security,
            config,
            current_language,
            session_vars: HashMap::new(),
            command_aliases: Self::default_aliases(),
            auto_complete_enabled: true,
            session_id,
            conversation_context: Vec::new(),
            current_code: None,
            code_generation_mode: false,
        })
    }

    fn default_aliases() -> HashMap<String, String> {
        let mut aliases = HashMap::new();
        aliases.insert("ll".to_string(), "ls -la".to_string());
        aliases.insert("la".to_string(), "ls -la".to_string());
        aliases.insert("..".to_string(), "cd ..".to_string());
        aliases.insert("cls".to_string(), "clear".to_string());
        aliases.insert("h".to_string(), "history".to_string());
        aliases.insert("q".to_string(), "quit".to_string());
        aliases
    }

    pub fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.welcome();
        LOGGER.info(&format!("Interactive session started (ID: {})", self.session_id));
        LOGGER.info("Enhanced terminal interaction mode - Type 'help' for commands, 'quit' to exit");
        LOGGER.separator();

        self.show_session_info();

        loop {
            self.display_prompt();
            
            let mut input = String::new();
            match io::stdin().read_line(&mut input) {
                Ok(_) => {
                    let command = input.trim();
                    
                    if command.is_empty() {
                        continue;
                    }
                    
                    // Handle exit commands
                    if matches!(command.to_lowercase().as_str(), "exit" | "quit" | "q") {
                        LOGGER.goodbye();
                        break;
                    }
                    
                    // Process the command
                    if let Err(e) = self.process_command(command) {
                        LOGGER.error(&format!("Command processing error: {}", e));
                    }
                }
                Err(error) => {
                    LOGGER.error(&format!("Error reading input: {}", error));
                    break;
                }
            }
        }
        
        Ok(())
    }

    fn display_prompt(&self) {
        let current_dir = std::env::current_dir()
            .map(|p| p.file_name().unwrap_or_default().to_string_lossy().to_string())
            .unwrap_or_else(|_| "unknown".to_string());
        
        print!("\n🤖 [{}:{}] {} ", 
            self.current_language.to_string().to_lowercase(),
            current_dir,
            ">".bright_blue()
        );
        io::stdout().flush().unwrap();
    }

    fn show_session_info(&self) {
        LOGGER.section("Session Information");
        println!("  Language: {}", self.current_language);
        println!("  Model: {}", self.config.get_effective_model(&self.current_language));
        println!("  Timeout: {}s", self.config.get_effective_timeout(&self.current_language));
        println!("  Testing: {}", if self.config.get_effective_testing(&self.current_language) { "Enabled" } else { "Disabled" });
        println!("  Auto-complete: {}", if self.auto_complete_enabled { "Enabled" } else { "Disabled" });
    }

    fn process_command(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Handle command chaining (commands separated by &&, ||, or ;)
        if input.contains("&&") || input.contains("||") || input.contains(";") {
            return self.process_command_chain(input);
        }

        // Expand aliases
        let expanded_input = self.expand_aliases(input);
        
        // Handle built-in commands
        if self.handle_builtin_command(&expanded_input)? {
            return Ok(());
        }

        // Check if this looks like a code generation request
        if self.is_code_generation_request(&expanded_input) {
            return self.handle_code_generation_request(&expanded_input);
        }

        // Handle simple shell commands directly (for quick operations)
        if self.is_simple_shell_command(&expanded_input) {
            let result = self.bridge.execute_interactive_command(&expanded_input);
            if result.success {
                if !result.stdout.is_empty() {
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error(&format!("Command failed: {}", result.stderr));
            }
            return Ok(());
        }

        // Show suggestions if enabled
        if self.auto_complete_enabled {
            let suggestions = self.memory.get_suggestions(&expanded_input);
            if !suggestions.is_empty() && suggestions[0] != expanded_input {
                println!("💡 Suggestions: {}", suggestions.join(", "));
            }
        }

        // Plan the command
        let plan = self.planner.plan_command(&expanded_input);
        
        // Show plan summary
        LOGGER.info(&format!("Intent: {} | Risk: {} | Commands: {}", 
            plan.intent, plan.risk_level, plan.commands.len()));

        // Check if confirmation is needed
        if self.memory.should_confirm(&plan) {
            if !self.memory.request_confirmation(&plan) {
                LOGGER.info("Command execution cancelled by user");
                return Ok(());
            }
        }

        // Execute the plan with progress tracking
        let total_commands = plan.commands.len();
        if total_commands > 1 {
            LOGGER.info(&format!("Executing {} commands...", total_commands));
        }

        let results = self.bridge.execute_plan(&plan);

        // Display results
        for (i, result) in results.iter().enumerate() {
            if total_commands > 1 {
                LOGGER.progress_bar(i + 1, total_commands, &format!("Command {}/{}", i + 1, total_commands));
            }
            if result.success {
                if !result.stdout.is_empty() {
                    LOGGER.output_header();
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error_header();
                if !result.stderr.is_empty() {
                    println!("{}", result.stderr);
                }
            }
        }

        // Add to memory
        self.memory.add_execution(expanded_input, plan, results);
        
        Ok(())
    }

    fn process_command_chain(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.info("Processing command chain");
        
        // Simple chain parsing (can be enhanced)
        let commands: Vec<&str> = if input.contains("&&") {
            input.split("&&").collect()
        } else if input.contains("||") {
            input.split("||").collect()
        } else {
            input.split(";").collect()
        };

        for (i, cmd) in commands.iter().enumerate() {
            let cmd = cmd.trim();
            if cmd.is_empty() {
                continue;
            }
            
            LOGGER.info(&format!("Executing command {}/{}: {}", i + 1, commands.len(), cmd));
            
            if let Err(e) = self.process_command(cmd) {
                LOGGER.error(&format!("Command chain failed at step {}: {}", i + 1, e));
                break;
            }
        }
        
        Ok(())
    }

    fn expand_aliases(&self, input: &str) -> String {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return input.to_string();
        }

        if let Some(alias_expansion) = self.command_aliases.get(parts[0]) {
            if parts.len() > 1 {
                format!("{} {}", alias_expansion, parts[1..].join(" "))
            } else {
                alias_expansion.clone()
            }
        } else {
            input.to_string()
        }
    }

    fn handle_builtin_command(&mut self, input: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return Ok(false);
        }

        match parts[0] {
            "help" => {
                self.show_enhanced_help();
                Ok(true)
            }
            "history" => {
                let limit = if parts.len() > 1 {
                    parts[1].parse().ok()
                } else {
                    None
                };
                self.memory.show_history(limit);
                Ok(true)
            }
            "alias" => {
                if parts.len() == 3 {
                    self.command_aliases.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Alias created: {} -> {}", parts[1], parts[2]));
                } else {
                    println!("Current aliases:");
                    for (alias, command) in &self.command_aliases {
                        println!("  {} -> {}", alias, command);
                    }
                }
                Ok(true)
            }
            "set" => {
                if parts.len() == 3 {
                    self.session_vars.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Variable set: {}={}", parts[1], parts[2]));
                } else {
                    println!("Current session variables:");
                    for (key, value) in &self.session_vars {
                        println!("  {}={}", key, value);
                    }
                }
                Ok(true)
            }
            "autocomplete" => {
                if parts.len() > 1 {
                    match parts[1] {
                        "on" | "enable" => {
                            self.auto_complete_enabled = true;
                            LOGGER.success("Auto-complete enabled");
                        }
                        "off" | "disable" => {
                            self.auto_complete_enabled = false;
                            LOGGER.success("Auto-complete disabled");
                        }
                        _ => {
                            LOGGER.error("Usage: autocomplete [on|off]");
                        }
                    }
                } else {
                    println!("Auto-complete is {}", if self.auto_complete_enabled { "enabled" } else { "disabled" });
                }
                Ok(true)
            }
            "status" => {
                self.show_session_info();
                Ok(true)
            }
            "clear" => {
                print!("\x1B[2J\x1B[1;1H"); // ANSI clear screen
                Ok(true)
            }
            "security" => {
                self.handle_security_command(&parts[1..])?;
                Ok(true)
            }
            "ps" => {
                let processes = self.bridge.list_active_processes();
                if processes.is_empty() {
                    LOGGER.info("No active processes");
                } else {
                    println!("Active processes: {:?}", processes);
                }
                Ok(true)
            }
            "kill" => {
                if parts.len() > 1 {
                    if let Ok(pid) = parts[1].parse::<u32>() {
                        match self.bridge.kill_process(pid) {
                            Ok(_) => LOGGER.success(&format!("Process {} terminated", pid)),
                            Err(e) => LOGGER.error(&format!("Failed to kill process {}: {}", pid, e)),
                        }
                    } else {
                        LOGGER.error("Invalid process ID");
                    }
                } else {
                    LOGGER.error("Usage: kill <pid>");
                }
                Ok(true)
            }
            "timeout" => {
                if parts.len() > 1 {
                    if let Ok(timeout) = parts[1].parse::<u64>() {
                        let new_config = crate::terminal_bridge::ExecutionConfig {
                            timeout_seconds: timeout,
                            capture_output: true,
                            working_directory: None,
                            environment_vars: std::collections::HashMap::new(),
                            max_output_lines: Some(100),
                            stream_output: false,
                        };
                        self.bridge.update_config(new_config);
                        LOGGER.success(&format!("Execution timeout updated to {}s", timeout));
                    } else {
                        LOGGER.error("Invalid timeout value");
                    }
                } else {
                    LOGGER.error("Usage: timeout <seconds>");
                }
                Ok(true)
            }
            _ => Ok(false)
        }
    }

    fn handle_security_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if args.is_empty() {
            self.security.show_policy();
            return Ok(());
        }

        match args[0] {
            "allow" => {
                if args.len() > 1 {
                    self.security.add_allowed_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security allow <command>");
                }
            }
            "block" => {
                if args.len() > 1 {
                    self.security.add_blocked_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security block <command>");
                }
            }
            "unallow" => {
                if args.len() > 1 {
                    self.security.remove_allowed_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unallow <command>");
                }
            }
            "unblock" => {
                if args.len() > 1 {
                    self.security.remove_blocked_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unblock <command>");
                }
            }
            "audit" => {
                let limit = if args.len() > 1 {
                    args[1].parse().ok()
                } else {
                    None
                };
                self.security.show_audit_log(limit);
            }
            "dry-run" => {
                if args.len() > 1 {
                    match args[1] {
                        "on" | "enable" => {
                            self.security.enable_dry_run();
                        }
                        "off" | "disable" => {
                            self.security.disable_dry_run();
                        }
                        _ => {
                            LOGGER.error("Usage: security dry-run [on|off]");
                        }
                    }
                } else {
                    let status = if self.security.is_dry_run_enabled() { "enabled" } else { "disabled" };
                    LOGGER.info(&format!("Dry-run mode is {}", status));
                }
            }
            "sandbox" => {
                self.security.create_sandbox_environment()?;
            }
            "help" => {
                self.show_security_help();
            }
            _ => {
                LOGGER.error(&format!("Unknown security command: {}", args[0]));
                self.show_security_help();
            }
        }

        Ok(())
    }

    fn is_simple_shell_command(&self, input: &str) -> bool {
        let simple_commands = [
            "ls", "pwd", "whoami", "date", "echo", "cat", "head", "tail",
            "grep", "find", "which", "uname", "df", "du", "free", "uptime"
        ];

        let first_word = input.split_whitespace().next().unwrap_or("");
        simple_commands.contains(&first_word)
    }

    fn show_security_help(&self) {
        LOGGER.section("Security Commands");
        println!("🔒 Available security commands:");
        println!("  • security - Show current security policy");
        println!("  • security allow <cmd> - Add command to allowlist");
        println!("  • security block <cmd> - Add command to blocklist");
        println!("  • security unallow <cmd> - Remove command from allowlist");
        println!("  • security unblock <cmd> - Remove command from blocklist");
        println!("  • security audit [n] - Show security audit log (last n entries)");
        println!("  • security dry-run [on|off] - Enable/disable dry-run mode");
        println!("  • security sandbox - Create sandbox environment");
        println!("  • security help - Show this help");
    }

    fn show_enhanced_help(&self) {
        LOGGER.section("Enhanced Interactive Commands");
        
        println!("🎯 Code Generation:");
        println!("  • Just type your request: 'create a fibonacci function'");
        println!("  • Chain commands: 'create function && run tests'");
        println!();
        
        println!("🔧 Built-in Commands:");
        println!("  • help - Show this help");
        println!("  • history [n] - Show command history (last n entries)");
        println!("  • status - Show current session status");
        println!("  • alias [name] [command] - Create or list aliases");
        println!("  • set [var] [value] - Set or list session variables");
        println!("  • autocomplete [on|off] - Toggle auto-completion");
        println!("  • clear - Clear screen");
        println!("  • security - Security management commands");
        println!("  • ps - List active processes");
        println!("  • kill <pid> - Terminate a process");
        println!("  • timeout <seconds> - Update execution timeout");
        println!();
        
        println!("⚡ Command Chaining:");
        println!("  • cmd1 && cmd2 - Run cmd2 only if cmd1 succeeds");
        println!("  • cmd1 || cmd2 - Run cmd2 only if cmd1 fails");
        println!("  • cmd1 ; cmd2 - Run both commands sequentially");
        println!();
        
        println!("🎨 Current Aliases:");
        for (alias, command) in &self.command_aliases {
            println!("  • {} -> {}", alias, command);
        }

        println!();
        println!("🤖 Code Generation Commands:");
        println!("  • generate <task> - Generate code for a specific task");
        println!("  • refine <changes> - Refine the current generated code");
        println!("  • explain - Explain the current generated code");
        println!("  • test - Test the current generated code");
        println!("  • save [filename] - Save the current code to a file");
        println!("  • show - Show the current generated code");
        println!("  • lang <language> - Change the programming language");
    }

    // Enhanced code generation methods
    fn is_code_generation_request(&self, input: &str) -> bool {
        let code_keywords = [
            "generate", "create", "write", "build", "make", "implement",
            "function", "class", "script", "program", "code", "algorithm",
            "refine", "improve", "modify", "change", "update", "fix",
            "explain", "show", "test", "run"
        ];

        let input_lower = input.to_lowercase();
        code_keywords.iter().any(|&keyword| input_lower.contains(keyword)) ||
        input.starts_with("generate ") ||
        input.starts_with("refine ") ||
        input.starts_with("explain") ||
        input.starts_with("test") ||
        input.starts_with("show") ||
        input.starts_with("save")
    }

    fn handle_code_generation_request(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        let input_lower = input.to_lowercase();

        if input_lower.starts_with("generate ") {
            let task = &input[9..]; // Remove "generate "
            self.generate_code(task)
        } else if input_lower.starts_with("refine ") {
            let changes = &input[7..]; // Remove "refine "
            self.refine_current_code(changes)
        } else if input_lower == "explain" {
            self.explain_current_code()
        } else if input_lower == "test" {
            self.test_current_code()
        } else if input_lower == "show" {
            self.show_current_code()
        } else if input_lower.starts_with("save") {
            let filename = if input.len() > 5 { Some(input[5..].trim()) } else { None };
            self.save_current_code(filename)
        } else if input_lower.starts_with("lang ") {
            let language = &input[5..];
            self.change_language(language)
        } else {
            // Treat as a general code generation request
            self.generate_code(input)
        }
    }

    fn generate_code(&mut self, task: &str) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.info(&format!("🤖 Generating code for: {}", task));

        // Add to conversation context
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Generate code using the existing agent
        let model = self.config.get_effective_model(&self.current_language);
        let constraints = self.current_language.get_prompt_context();

        LOGGER.info("🔄 Calling code generation...");
        agent::generate_code_via_python(task, &model, &self.current_language, constraints);

        // Try to read the generated code
        let filename = self.current_language.output_filename();
        match fs::read_to_string(&filename) {
            Ok(code_content) => {
                LOGGER.success("✅ Code generated successfully!");

                // Store the generated code
                self.current_code = Some(GeneratedCode {
                    content: code_content.clone(),
                    language: self.current_language.clone(),
                    filename: filename.clone(),
                    task_description: task.to_string(),
                    last_modified: timestamp,
                });

                // Add to conversation context
                self.conversation_context.push(ConversationEntry {
                    user_input: task.to_string(),
                    response: "Code generated successfully".to_string(),
                    timestamp,
                    code_generated: Some(code_content.clone()),
                });

                // Show the generated code
                LOGGER.section("Generated Code");
                println!("{}", code_content);
                LOGGER.separator();

                LOGGER.info("💡 You can now use: 'test', 'refine <changes>', 'explain', or 'save [filename]'");
            }
            Err(e) => {
                LOGGER.error(&format!("Failed to read generated code: {}", e));
                return Err(Box::new(e));
            }
        }

        Ok(())
    }

    fn refine_current_code(&mut self, changes: &str) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code.clone() {
            LOGGER.info(&format!("🔧 Refining code with: {}", changes));

            // Create a refinement prompt
            let refinement_task = format!(
                "Refine this {} code:\n\n```{}\n{}\n```\n\nChanges requested: {}",
                self.current_language.to_string().to_lowercase(),
                self.current_language.file_extension(),
                current.content,
                changes
            );

            self.generate_code(&refinement_task)
        } else {
            LOGGER.error("No code to refine. Generate code first with 'generate <task>'");
            Ok(())
        }
    }

    fn explain_current_code(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.section("Code Explanation");
            println!("📝 Task: {}", current.task_description);
            println!("🔧 Language: {}", current.language);
            println!("📁 File: {}", current.filename);
            println!("🕒 Generated: {}", self.format_timestamp(current.last_modified));
            println!();
            println!("📋 Code:");
            println!("{}", current.content);
            LOGGER.separator();
        } else {
            LOGGER.error("No code to explain. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn test_current_code(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.info("🧪 Testing generated code...");

            // Use the existing runner to test the code
            let config = crate::runner::ExecutionConfig {
                timeout_seconds: self.config.default_timeout,
                max_memory_mb: Some(512),
            };

            crate::runner::run_generated_code_with_config(&current.language, &config);
        } else {
            LOGGER.error("No code to test. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn show_current_code(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            LOGGER.section(&format!("Current {} Code", current.language));
            println!("{}", current.content);
            LOGGER.separator();
        } else {
            LOGGER.info("No code generated yet. Use 'generate <task>' to create code.");
        }
        Ok(())
    }

    fn save_current_code(&self, filename: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(ref current) = self.current_code {
            let save_filename = filename.unwrap_or(&current.filename);

            fs::write(save_filename, &current.content)?;
            LOGGER.success(&format!("💾 Code saved to: {}", save_filename));
        } else {
            LOGGER.error("No code to save. Generate code first with 'generate <task>'");
        }
        Ok(())
    }

    fn change_language(&mut self, language_str: &str) -> Result<(), Box<dyn std::error::Error>> {
        match Language::from_str(language_str) {
            Ok(new_language) => {
                self.current_language = new_language.clone();
                LOGGER.success(&format!("🔧 Language changed to: {}", new_language));
            }
            Err(e) => {
                LOGGER.error(&format!("Invalid language: {}", e));
                LOGGER.info("Available languages: Python, Rust, JavaScript, TypeScript, Go, Java, C#, C++, C");
            }
        }
        Ok(())
    }

    fn format_timestamp(&self, timestamp: u64) -> String {
        // Simple timestamp formatting - could be enhanced
        format!("{} seconds ago",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() - timestamp
        )
    }
}

use colored::*; // Add this import for the bright_blue() method
